<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎥 Subir PPTX - Demo Local</title>
    <link rel="stylesheet" href="slideshow-video.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .upload-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .upload-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .upload-zone {
            border: 3px dashed #4672c4;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-zone:hover {
            border-color: #3f51b5;
            background: #f0f2ff;
        }
        
        .upload-zone.dragover {
            border-color: #28a745;
            background: #f0fff4;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #4672c4;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .upload-button {
            background: #4672c4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-button:hover {
            background: #3f51b5;
            transform: translateY(-2px);
        }
        
        .file-info {
            background: #e8f4fd;
            border: 1px solid #4672c4;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            display: none;
        }
        
        .processing {
            text-align: center;
            padding: 20px;
            display: none;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4672c4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .slideshow-container {
            margin-top: 30px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            display: none;
        }
        
        .demo-slides {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .slide-preview {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin: 8px 0;
            background: white;
        }
        
        .slide-number {
            background: #4672c4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        
        .slide-info {
            flex: 1;
        }
        
        .slide-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .type-image { background: #e3f2fd; color: #1976d2; }
        .type-video { background: #fff3e0; color: #f57c00; }
        .type-mixed { background: #f3e5f5; color: #7b1fa2; }
        
        .error-message {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        .success-message {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="upload-container">
        <div class="upload-header">
            <h1>🎥 Subir Presentación PPTX</h1>
            <p>Sube tu archivo PPTX para convertirlo en slideshow con videos</p>
        </div>
        
        <div class="upload-zone" id="uploadZone">
            <div class="upload-icon">📁</div>
            <div class="upload-text">
                Arrastra tu archivo PPTX aquí o haz click para seleccionar
            </div>
            <button class="upload-button" onclick="document.getElementById('fileInput').click()">
                Seleccionar Archivo
            </button>
            <input type="file" id="fileInput" accept=".pptx,.ppt" style="display: none;">
        </div>
        
        <div class="file-info" id="fileInfo">
            <h3>📄 Archivo Seleccionado:</h3>
            <p id="fileName"></p>
            <p id="fileSize"></p>
            <button class="upload-button" onclick="processFile()">
                🚀 Procesar Presentación
            </button>
        </div>
        
        <div class="processing" id="processing">
            <div class="spinner"></div>
            <h3>🔄 Procesando PPTX...</h3>
            <p>Extrayendo slides, videos y contenido multimedia...</p>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <div class="demo-slides" id="demoSlides" style="display: none;">
            <h3>📊 Contenido Extraído (Simulado):</h3>
            <div id="slidesList"></div>
            <button class="upload-button" onclick="launchSlideshow()" style="margin-top: 15px;">
                🎬 Iniciar Slideshow
            </button>
        </div>
        
        <div class="slideshow-container" id="slideshowContainer">
            <div class="demo-slideshow" style="height: 600px; display: flex; justify-content: center; align-items: center;"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="slideshow-video.js"></script>
    <script>
        let selectedFile = null;
        let processedSlides = [];

        // Simulación de window.tool
        window.tool = {
            hasClassName: (event, className) => {
                return event.target.classList.contains(className);
            },
            template: (array, callback) => {
                return array.map(callback).join('');
            }
        };

        // Configurar drag & drop
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');

        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        uploadZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file.name.toLowerCase().endsWith('.pptx') && !file.name.toLowerCase().endsWith('.ppt')) {
                showError('Por favor selecciona un archivo PPTX o PPT válido.');
                return;
            }

            selectedFile = file;
            document.getElementById('fileName').textContent = `Nombre: ${file.name}`;
            document.getElementById('fileSize').textContent = `Tamaño: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
            document.getElementById('fileInfo').style.display = 'block';
            hideMessages();
        }

        function processFile() {
            if (!selectedFile) {
                showError('No hay archivo seleccionado.');
                return;
            }

            // Mostrar procesamiento
            document.getElementById('processing').style.display = 'block';
            document.getElementById('fileInfo').style.display = 'none';
            hideMessages();

            // Simular procesamiento (en la vida real aquí iría la lógica de extracción)
            setTimeout(() => {
                simulateProcessing();
            }, 2000);
        }

        function simulateProcessing() {
            // Simular extracción de contenido del PPTX
            const slideCount = Math.floor(Math.random() * 8) + 3; // 3-10 slides
            processedSlides = [];

            for (let i = 0; i < slideCount; i++) {
                const slideTypes = ['image', 'video', 'mixed'];
                const randomType = slideTypes[Math.floor(Math.random() * slideTypes.length)];
                
                let slide = {
                    type: randomType,
                    note: `<h3>Slide ${i + 1}: ${getSlideTitle(randomType)}</h3><p>Contenido extraído del PPTX original.</p><ul><li>Punto importante 1</li><li>Punto importante 2</li></ul>`,
                    hasVideo: randomType !== 'image'
                };

                if (randomType === 'image') {
                    slide.imgUrl = '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
                } else if (randomType === 'video') {
                    slide.videoUrl = getRandomVideoUrl();
                    slide.thumbnailUrl = getRandomThumbnailUrl();
                } else if (randomType === 'mixed') {
                    slide.imgUrl = '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms';
                    slide.videoUrl = getRandomVideoUrl();
                    slide.isMixed = true;
                }

                processedSlides.push(slide);
            }

            // Ocultar procesamiento y mostrar resultados
            document.getElementById('processing').style.display = 'none';
            showProcessedSlides();
            showSuccess(`¡Procesamiento completado! Se extrajeron ${slideCount} slides con ${processedSlides.filter(s => s.hasVideo).length} videos.`);
        }

        function getSlideTitle(type) {
            const titles = {
                image: 'Contenido Visual',
                video: 'Video Embebido',
                mixed: 'Contenido Mixto'
            };
            return titles[type] || 'Slide';
        }

        function getRandomVideoUrl() {
            const videos = [
                'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
                'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4'
            ];
            return videos[Math.floor(Math.random() * videos.length)];
        }

        function getRandomThumbnailUrl() {
            const thumbnails = [
                'https://sample-videos.com/zip/10/jpg/SampleJPGImage_1280x720_1mb.jpg',
                'https://storage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg'
            ];
            return thumbnails[Math.floor(Math.random() * thumbnails.length)];
        }

        function showProcessedSlides() {
            const slidesList = document.getElementById('slidesList');
            slidesList.innerHTML = '';

            processedSlides.forEach((slide, index) => {
                const slideDiv = document.createElement('div');
                slideDiv.className = 'slide-preview';
                slideDiv.innerHTML = `
                    <div class="slide-number">${index + 1}</div>
                    <div class="slide-info">
                        <strong>Slide ${index + 1}</strong>
                        <span class="slide-type type-${slide.type}">${slide.type.toUpperCase()}</span>
                        <br>
                        <small>${slide.hasVideo ? '🎥 Contiene video' : '🖼️ Solo imagen'}</small>
                    </div>
                `;
                slidesList.appendChild(slideDiv);
            });

            document.getElementById('demoSlides').style.display = 'block';
        }

        function launchSlideshow() {
            document.getElementById('slideshowContainer').style.display = 'block';
            
            // Scroll al slideshow
            document.getElementById('slideshowContainer').scrollIntoView({ 
                behavior: 'smooth' 
            });

            // Inicializar slideshow
            const slideshow = new VideoSlideshow({
                target: 'demo-slideshow',
                slides: processedSlides,
                id: `pptx-${Date.now()}`,
                title: selectedFile.name.replace(/\.(pptx|ppt)$/i, ''),
                translation: {
                    slideshow: ['Página', 'Pantalla para estudiantes', 'Abrir zoom', 'Ir a la libreria', 'Pantalla completa']
                }
            });

            console.log('🎬 Slideshow iniciado con', processedSlides.length, 'slides del archivo:', selectedFile.name);
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }
    </script>
</body>
</html>
